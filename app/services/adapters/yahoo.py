from typing import List, Dict, Any, Optional
import requests
import os
from app.services.adapters.mock_provider import MockMarketAdapter
from app.data_access.repositories import get_provider_config


class YahooAdapter(MockMarketAdapter):
    """
    Yahoo!ショッピングAPIアダプター
    API Documentation: https://developer.yahoo.co.jp/webapi/shopping/v3/itemsearch.html

    対応API:
    - 商品検索API (v3)
    """

    def __init__(self):
        """
        認証情報を設定から取得
        """
        config = get_provider_config("yahoo")
        if config:
            self.app_id = config.get("api_key")  # Yahoo! Client ID (アプリケーションID)
        else:
            # 環境変数からフォールバック
            self.app_id = os.environ.get("YAHOO_API_KEY")

        self.base_url = "https://shopping.yahooapis.jp/ShoppingWebService/V3/itemSearch"

    def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Yahoo!ショッピングAPIへのリクエストを実行
        """
        if not self.app_id:
            print("Yahoo API key not configured, falling back to mock data")
            return {"error": "API key not configured"}

        # 必須パラメータを追加
        request_params = {"appid": self.app_id, **params}

        print(f"Making request to Yahoo Shopping API")
        print(f"Parameters: {request_params}")

        try:
            response = requests.get(
                self.base_url,
                params=request_params,
                timeout=30,
                headers={"User-Agent": "EC-SaaS/1.0"},
            )

            print(f"Response status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            print(
                f"Response keys: {list(result.keys()) if isinstance(result, dict) else type(result)}"
            )
            return result

        except requests.exceptions.RequestException as e:
            print(f"Yahoo Shopping API request error: {e}")
            return {"error": str(e)}
        except Exception as e:
            print(f"Yahoo Shopping API error: {e}")
            return {"error": str(e)}

    def _parse_yahoo_items(
        self, api_response: Dict[str, Any], query: str
    ) -> List[Dict[str, Any]]:
        """
        Yahoo!ショッピングAPIのレスポンスを標準形式に変換
        """
        if "error" in api_response:
            print(f"API error in response: {api_response['error']}")
            return []

        hits = api_response.get("hits", [])
        if not hits:
            print("No hits found in Yahoo Shopping API response")
            return []

        results = []
        for i, item in enumerate(hits):
            try:
                # 価格情報の取得
                price = item.get("price", 0)

                # 画像URLの取得
                image_url = ""
                if item.get("image"):
                    image_url = item["image"].get(
                        "medium", item["image"].get("small", "")
                    )
                elif item.get("exImage"):
                    image_url = item["exImage"].get("url", "")

                # レビュー情報の取得
                review_info = item.get("review", {})
                review_count = review_info.get("count", 0)
                review_rate = review_info.get("rate", 0.0)

                # ブランド情報の取得
                brand_info = item.get("brand", {})
                brand_name = brand_info.get("name", "")

                # ストア情報の取得
                seller_info = item.get("seller", {})
                shop_name = seller_info.get("name", "")
                shop_id = seller_info.get("sellerId", "")

                # ジャンル情報の取得
                genre_info = item.get("genreCategory", {})
                genre_name = genre_info.get("name", "")

                results.append(
                    {
                        "listing_id": item.get("code", f"YAHOO-{i}"),
                        "sku": item.get("code", f"SKU-{i}"),
                        "title": item.get("name", f"{query} - Yahoo商品{i}"),
                        "price": price,
                        "stock": (
                            100 if item.get("inStock", True) else 0
                        ),  # Yahoo APIでは在庫数は取得できない
                        "reviews": review_count,
                        "rating": review_rate,
                        "url": item.get("url", ""),
                        "shop_name": shop_name,
                        "shop_id": shop_id,
                        "brand_name": brand_name,
                        "genre_name": genre_name,
                        "image_url": image_url,
                        "description": item.get("description", ""),
                        "headline": item.get("headLine", ""),
                        "condition": item.get("condition", "new"),
                        "jan_code": item.get("janCode", ""),
                        "affiliate_rate": item.get("affiliateRate", 0.0),
                        "premium_price": item.get("premiumPrice", price),
                        "is_best_seller": seller_info.get("isBestSeller", False),
                        "shipping_code": item.get("shipping", {}).get("code", 1),
                        "payment_methods": item.get("payment", ""),
                    }
                )

            except Exception as e:
                print(f"Error parsing Yahoo item {i}: {e}")
                continue

        print(f"Successfully parsed {len(results)} items from Yahoo Shopping API")
        return results

    def search_products(
        self,
        query: str,
        results: int = 20,
        start: int = 1,
        price_from: Optional[int] = None,
        price_to: Optional[int] = None,
        sort: str = "-score",
        in_stock: Optional[bool] = None,
        **extra_params: Any,
    ) -> List[Dict[str, Any]]:
        """
        Yahoo!ショッピング商品検索

        Args:
            query: 検索キーワード
            results: 取得件数 (1-100, デフォルト: 20)
            start: 開始位置 (デフォルト: 1)
            price_from: 価格下限
            price_to: 価格上限
            sort: ソート順 (-score, +price, -price, -review_count)
            in_stock: 在庫有無 (True: 在庫あり, False: 在庫なし, None: 指定なし)
            **extra_params: その他のAPIパラメータ
        """
        if not self.app_id:
            print("Yahoo API key not configured, using mock data")
            return super().search_products(query)

        params: Dict[str, Any] = {
            "query": query,
            "results": min(max(results, 1), 100),  # 1-100の範囲に制限
            "start": max(start, 1),
            "sort": sort,
        }

        # オプションパラメータの追加
        if price_from is not None:
            params["price_from"] = price_from
        if price_to is not None:
            params["price_to"] = price_to
        if in_stock is not None:
            params["in_stock"] = in_stock

        # 追加パラメータ
        for key, value in extra_params.items():
            if value is not None:
                params[key] = value

        api_response = self._make_request(params)
        return self._parse_yahoo_items(api_response, query)
