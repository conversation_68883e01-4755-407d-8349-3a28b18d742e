#!/usr/bin/env python3
"""
Yahoo Shopping API Adapter Test

このスクリプトはYahoo Shopping APIアダプターの動作をテストします。

実行方法:
python app/services/adapters/tests/test_yahoo.py

必要な環境変数:
export YAHOO_API_KEY='your_yahoo_client_id'
"""

import os
import sys
import json
from typing import Dict, Any, List

# プロジェクトルートをPythonパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../.."))

from app.services.adapters.yahoo import YahooAdapter


def print_separator(title: str):
    """セクション区切りを表示"""
    print("\n" + "=" * 60)
    print(f"🛒 {title}")
    print("=" * 60)


def print_product_summary(products: List[Dict[str, Any]], max_items: int = 5):
    """商品リストの概要を表示"""
    if not products:
        print("❌ 商品が見つかりませんでした")
        return

    print(f"✅ 検索結果: {len(products)}件")
    for i, product in enumerate(products[:max_items]):
        price = product.get("price", 0)
        title = product.get("title", "タイトル不明")[:50]
        shop = product.get("shop_name", "不明")
        reviews = product.get("reviews", 0)
        rating = product.get("rating", 0.0)

        print(f"  {i+1}. {title}...")
        print(f"     価格: ¥{price:,} | ショップ: {shop}")
        print(f"     レビュー: {reviews}件 | 評価: {rating}★")


def test_basic_search():
    """基本的な商品検索テスト"""
    print_separator("基本商品検索テスト")

    adapter = YahooAdapter()

    # Nintendo Switchで検索
    print("🔍 商品検索テスト (query: Nintendo Switch)")
    products = adapter.search_products("Nintendo Switch", results=5)
    print_product_summary(products)

    return products


def test_price_range_search():
    """価格帯検索テスト"""
    print_separator("価格帯検索テスト")

    adapter = YahooAdapter()

    # 1000-5000円の商品を検索
    print("💰 価格帯検索テスト (1000-5000円)")
    products = adapter.search_products(
        "ゲーム", price_from=1000, price_to=5000, results=5
    )
    print_product_summary(products)

    return products


def main():
    """メイン実行関数"""
    print("🚀 Yahoo Shopping API Adapter テスト開始")

    # API キーの確認
    api_key = os.environ.get("YAHOO_API_KEY")
    if not api_key:
        print("⚠️  YAHOO_API_KEY環境変数が設定されていません")
        print("⚠️  モックデータでのテストになります")
    else:
        print(f"✅ Yahoo API Key: {api_key[:10]}...")

    try:
        # 各テストを実行
        test_basic_search()
        test_price_range_search()

        print_separator("テスト完了")
        print("✅ すべてのテストが完了しました")

    except Exception as e:
        print(f"\n❌ テスト中にエラーが発生しました: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
